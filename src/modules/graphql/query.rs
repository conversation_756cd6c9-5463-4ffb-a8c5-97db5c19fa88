use async_graphql::*;
use uuid::Uuid;
use crate::modules::graphql::{
    context::GraphQLContext,
    types::{Category, Laptop, LaptopDetailed, LaptopFilter},
};
use crate::errors::AppError;

/// Root Query cho GraphQL
#[derive(Default)]
pub struct Query;

#[Object]
impl Query {
    /// Lấy danh sách categories với phân trang
    async fn categories(
        &self,
        ctx: &Context<'_>,
        #[graphql(desc = "Số trang (bắt đầu từ 1)", default = 1)] page: u32,
        #[graphql(desc = "Số lượng items mỗi trang", default = 20)] size: u32,
    ) -> Result<Vec<Category>, Error> {
        let context = ctx.data::<GraphQLContext>()?;
        
        // Tạo pagination request
        let pagination_request = crate::modules::category::models::pagination::CategoryPaginationRequest {
            page: page as i64,
            limit: size as i64,
            ..Default::default()
        };
        
        // Sử dụng existing category service
        let categories = context
            .category_service
            .get_categories_with_pagination(pagination_request)
            .await
            .map_err(|e| Error::new(format!("Failed to fetch categories: {}", e)))?;

        Ok(categories.categories.into_iter().map(Category::from).collect())
    }

    /// Lấy danh sách laptops với filter và phân trang
    async fn laptops(
        &self,
        ctx: &Context<'_>,
        #[graphql(desc = "Bộ lọc laptop")] filter: Option<LaptopFilter>,
        #[graphql(desc = "Số trang (bắt đầu từ 1)", default = 1)] page: u32,
        #[graphql(desc = "Số lượng items mỗi trang", default = 20)] size: u32,
    ) -> Result<Vec<Laptop>, Error> {
        let context = ctx.data::<GraphQLContext>()?;
        
        // Tạo pagination request
        let pagination_request = crate::modules::laptop::models::LaptopPaginationRequest {
            page: Some(page as i64),
            per_page: Some(size as i64),
            search: filter.as_ref().and_then(|f| f.search.clone()),
            brand: filter.as_ref().and_then(|f| f.brand.clone()),
            status: filter.as_ref().and_then(|f| f.status),
            market_region: filter.as_ref().and_then(|f| f.market_region),
            ..Default::default()
        };
        
        let laptops = context
            .laptop_service
            .get_public_laptops(pagination_request)
            .await
            .map_err(|e| Error::new(format!("Failed to fetch laptops: {}", e)))?;

        Ok(laptops.laptops.into_iter().map(Laptop::from).collect())
    }

    /// Lấy laptop theo ID
    async fn laptop(
        &self,
        ctx: &Context<'_>,
        #[graphql(desc = "ID của laptop")] id: ID,
    ) -> Result<Option<LaptopDetailed>, Error> {
        let context = ctx.data::<GraphQLContext>()?;
        
        let laptop_id = Uuid::parse_str(&id)
            .map_err(|_| Error::new("Invalid laptop ID format"))?;

        match context.laptop_service.get_laptop_by_id(&laptop_id).await {
            Ok(laptop) => {
                // Convert LaptopFullView to LaptopDetailedView
                let detailed_view = crate::modules::laptop::models::LaptopDetailedView {
                    id: laptop.id,
                    category_id: laptop.category_id,
                    brand: laptop.brand,
                    model: laptop.model,
                    full_name: laptop.full_name,
                    slug: laptop.slug,
                    sku: laptop.sku,
                    market_region: laptop.market_region,
                    release_date: laptop.release_date,
                    description: laptop.description,
                    image_urls: laptop.image_urls,
                    status: laptop.status,
                    is_featured: laptop.is_featured,
                    view_count: laptop.view_count,
                    created_at: laptop.created_at,
                    updated_at: laptop.updated_at,
                };
                Ok(Some(LaptopDetailed::from(detailed_view)))
            },
            Err(AppError::NotFound(_)) => Ok(None),
            Err(e) => Err(Error::new(format!("Failed to fetch laptop: {}", e))),
        }
    }

    /// Lấy laptop theo slug
    async fn laptop_by_slug(
        &self,
        ctx: &Context<'_>,
        #[graphql(desc = "Slug của laptop")] slug: String,
    ) -> Result<Option<LaptopDetailed>, Error> {
        let context = ctx.data::<GraphQLContext>()?;

        match context.laptop_service.get_laptop_by_slug(&slug).await {
            Ok(laptop) => {
                // Convert LaptopFullView to LaptopDetailedView
                let detailed_view = crate::modules::laptop::models::LaptopDetailedView {
                    id: laptop.id,
                    category_id: laptop.category_id,
                    brand: laptop.brand,
                    model: laptop.model,
                    full_name: laptop.full_name,
                    slug: laptop.slug,
                    sku: laptop.sku,
                    market_region: laptop.market_region,
                    release_date: laptop.release_date,
                    description: laptop.description,
                    image_urls: laptop.image_urls,
                    status: laptop.status,
                    is_featured: laptop.is_featured,
                    view_count: laptop.view_count,
                    created_at: laptop.created_at,
                    updated_at: laptop.updated_at,
                };
                Ok(Some(LaptopDetailed::from(detailed_view)))
            },
            Err(AppError::NotFound(_)) => Ok(None),
            Err(e) => Err(Error::new(format!("Failed to fetch laptop: {}", e))),
        }
    }
}

