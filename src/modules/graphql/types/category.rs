use async_graphql::*;
use chrono::{DateTime, Utc};
use crate::modules::category::models::domain::{Category as DomainCategory, CategoryType};

/// GraphQL Category type
#[derive(SimpleObject)]
#[graphql(name = "Category")]
pub struct Category {
    pub id: ID,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub category_type: CategoryType,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl From<DomainCategory> for Category {
    fn from(domain_category: DomainCategory) -> Self {
        Self {
            id: ID(domain_category.id.to_string()),
            name: domain_category.name,
            slug: domain_category.slug,
            description: domain_category.description,
            category_type: domain_category.category_type,
            is_active: domain_category.is_active,
            created_at: domain_category.created_at,
            updated_at: domain_category.updated_at,
        }
    }
}