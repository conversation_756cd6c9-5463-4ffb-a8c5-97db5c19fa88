use async_graphql::*;
use chrono::{DateTime, Utc, NaiveDate};
use crate::modules::laptop::models::{LaptopPublicView, LaptopDetailedView};
use crate::schema::{MarketRegion, LaptopStatus};

/// GraphQL Laptop type
#[derive(SimpleObject)]
#[graphql(name = "Laptop")]
pub struct Laptop {
    pub id: ID,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub market_region: MarketRegion,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub view_count: i64,
}

/// GraphQL Laptop với thông tin chi tiết
#[derive(SimpleObject)]
#[graphql(name = "LaptopDetailed")]
pub struct LaptopDetailed {
    pub id: ID,
    pub category_id: ID,
    pub brand: String,
    pub model: String,
    pub full_name: String,
    pub slug: String,
    pub sku: Option<String>,
    pub market_region: MarketRegion,
    pub release_date: Option<NaiveDate>,
    pub description: Option<String>,
    pub image_urls: Vec<String>,
    pub status: LaptopStatus,
    pub is_featured: bool,
    pub view_count: i64,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

/// Input filter cho laptop queries
#[derive(InputObject)]
pub struct LaptopFilter {
    pub brand: Option<String>,
    pub status: Option<LaptopStatus>,
    pub market_region: Option<MarketRegion>,
    pub search: Option<String>,
}

impl From<LaptopPublicView> for Laptop {
    fn from(laptop: LaptopPublicView) -> Self {
        Self {
            id: ID(laptop.id.to_string()),
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            market_region: laptop.market_region,
            image_urls: laptop.image_urls,
            status: laptop.status,
            view_count: laptop.view_count,
        }
    }
}

impl From<LaptopDetailedView> for LaptopDetailed {
    fn from(laptop: LaptopDetailedView) -> Self {
        Self {
            id: ID(laptop.id.to_string()),
            category_id: ID(laptop.category_id.to_string()),
            brand: laptop.brand,
            model: laptop.model,
            full_name: laptop.full_name,
            slug: laptop.slug,
            sku: laptop.sku,
            market_region: laptop.market_region,
            release_date: laptop.release_date,
            description: laptop.description,
            image_urls: laptop.image_urls,
            status: laptop.status,
            is_featured: laptop.is_featured,
            view_count: laptop.view_count,
            created_at: laptop.created_at,
            updated_at: laptop.updated_at,
        }
    }
}