use std::sync::Arc;
use crate::modules::{
    category::service_trait::CategoryServiceTrait,
    laptop::service_trait::LaptopManagementServiceTrait,
};

/// GraphQL Context chứa các service cần thiết
#[derive(Clone)]
pub struct GraphQLContext {
    pub laptop_service: Arc<dyn LaptopManagementServiceTrait>,
    pub category_service: Arc<dyn CategoryServiceTrait>,
}

impl GraphQLContext {
    pub fn new(
        laptop_service: Arc<dyn LaptopManagementServiceTrait>,
        category_service: Arc<dyn CategoryServiceTrait>,
    ) -> Self {
        Self {
            laptop_service,
            category_service,
        }
    }
}